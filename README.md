# WenHaoFree Blog

A modern, responsive blog built with [<PERSON>](https://gohugo.io/) static site generator and the beautiful [LoveIt](https://github.com/dillonzq/LoveIt) theme.

## 🚀 Features

- **Fast & Lightweight**: Built with <PERSON> for blazing-fast performance
- **Responsive Design**: Looks great on desktop, tablet, and mobile devices
- **Modern Theme**: Clean and elegant LoveIt theme with dark/light mode support
- **SEO Optimized**: Built-in SEO optimization for better search engine visibility
- **Multilingual Support**: Ready for internationalization
- **Syntax Highlighting**: Beautiful code syntax highlighting
- **Social Integration**: Easy social media integration
- **Search Functionality**: Built-in search capabilities

## 📋 Prerequisites

Before you begin, ensure you have the following installed on your system:

- **Hugo Extended** (version 0.128.0 or higher)
  - Download from [Hugo Releases](https://github.com/gohugoio/hugo/releases)
  - Make sure to download the **extended** version for SCSS support
- **Git** for version control
- **Node.js** and **npm** (optional, for theme development)

### Installing Hugo

#### macOS
```bash
# Using Homebrew
brew install hugo

# Or using MacPorts
sudo port install hugo +universal
```

#### Windows
```bash
# Using Chocolatey
choco install hugo-extended

# Using Scoop
scoop install hugo-extended
```

#### Linux
```bash
# Using Snap
sudo snap install hugo

# Or download from GitHub releases
wget https://github.com/gohugoio/hugo/releases/download/v0.145.0/hugo_extended_0.145.0_linux-amd64.tar.gz
```

## 🛠️ Installation & Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/wenhaofree/blog-hugo.git
   cd blog-hugo
   ```

2. **Initialize and update git submodules** (for the theme)
   ```bash
   git submodule update --init --recursive
   ```

3. **Verify Hugo installation**
   ```bash
   hugo version
   ```

## 🏃‍♂️ Quick Start

### Development Server

Start the Hugo development server with live reload:

```bash
# Start development server
hugo server

# Start with drafts enabled
hugo server -D

# Start with specific environment
hugo server --environment development

# Start on specific port
hugo server --port 1314
```

The site will be available at `http://localhost:1313`

### Building for Production

Generate the static site for deployment:

```bash
# Build for production
hugo

# Build with minification
hugo --minify

# Build for specific environment
hugo --environment production --minify
```

The generated site will be in the `public/` directory.

## 📁 Project Structure

```
blog-hugo/
├── archetypes/          # Content templates
│   └── default.md
├── assets/              # Asset files (images, CSS, JS)
├── content/             # Content files
│   └── posts/           # Blog posts
├── data/                # Data files
├── i18n/                # Internationalization files
├── layouts/             # Custom layout templates
├── public/              # Generated static site (ignored in git)
├── resources/           # Hugo cache and generated resources
├── static/              # Static files (copied as-is)
├── themes/              # Hugo themes
│   └── LoveIt/          # LoveIt theme (git submodule)
├── hugo.yaml            # Hugo configuration
├── .gitignore           # Git ignore rules
└── README.md            # This file
```

## ✍️ Creating Content

### Creating a New Post

```bash
# Create a new post
hugo new posts/my-new-post.md

# Create a post in a specific directory
hugo new posts/tech/my-tech-post.md
```

### Post Front Matter Example

```yaml
---
title: "My Awesome Post"
date: 2025-06-25T10:00:00+08:00
draft: false
tags: ["hugo", "blog", "web"]
categories: ["Technology"]
author: "Your Name"
description: "A brief description of your post"
---

Your post content goes here...
```

### Content Organization

- Place blog posts in `content/posts/`
- Use subdirectories for organization (e.g., `content/posts/tech/`, `content/posts/life/`)
- Static files go in `static/` directory
- Images can be placed in `static/images/` or `assets/images/`

## ⚙️ Configuration

The main configuration file is `hugo.yaml`. Key settings include:

```yaml
baseURL: https://example.org/          # Your site URL
languageCode: en-us                    # Language code
title: WenHaoFree             # Site title
theme: ["LoveIt"]                      # Theme name
```

### Theme Configuration

The LoveIt theme offers extensive customization options. You can:

- Customize colors, fonts, and layout
- Configure social media links
- Set up analytics and comments
- Enable/disable features

Refer to the [LoveIt theme documentation](https://hugoloveit.com/) for detailed configuration options.

## 🚀 Deployment

### GitHub Pages

1. **Enable GitHub Pages** in your repository settings
2. **Set source** to GitHub Actions
3. **Create workflow file** `.github/workflows/hugo.yml`:

```yaml
name: Deploy Hugo site to Pages

on:
  push:
    branches: ["main"]
  workflow_dispatch:

permissions:
  contents: read
  pages: write
  id-token: write

concurrency:
  group: "pages"
  cancel-in-progress: false

defaults:
  run:
    shell: bash

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: Setup Hugo
        uses: peaceiris/actions-hugo@v2
        with:
          hugo-version: 'latest'
          extended: true

      - name: Setup Pages
        id: pages
        uses: actions/configure-pages@v3

      - name: Build with Hugo
        env:
          HUGO_ENVIRONMENT: production
          HUGO_ENV: production
        run: |
          hugo \
            --minify \
            --baseURL "${{ steps.pages.outputs.base_url }}/"

      - name: Upload artifact
        uses: actions/upload-pages-artifact@v2
        with:
          path: ./public

  deploy:
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest
    needs: build
    steps:
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v2
```

### Netlify

1. **Connect your repository** to Netlify
2. **Set build settings**:
   - Build command: `hugo --minify`
   - Publish directory: `public`
   - Environment variables: `HUGO_VERSION=0.145.0`

### Vercel

1. **Import your repository** to Vercel
2. **Configure build settings**:
   - Framework Preset: Hugo
   - Build Command: `hugo --minify`
   - Output Directory: `public`

## 🛠️ Development

### Theme Development

If you want to customize the theme:

```bash
# Navigate to theme directory
cd themes/LoveIt

# Install dependencies
npm install

# Build theme assets
npm run compile
```

### Local Development Tips

- Use `hugo server -D` to include draft posts
- Use `hugo server --disableFastRender` for more accurate rendering
- Check `hugo server --help` for more options

### Performance Optimization

- Optimize images before adding them
- Use Hugo's image processing features
- Minify CSS and JS in production
- Enable gzip compression on your server

## 🔧 Troubleshooting

### Common Issues

1. **Hugo version compatibility**
   - Ensure you're using Hugo Extended version 0.128.0+
   - Check with `hugo version`

2. **Theme not loading**
   - Verify git submodules are initialized: `git submodule update --init --recursive`
   - Check theme configuration in `hugo.yaml`

3. **Build errors**
   - Clear Hugo cache: `hugo mod clean`
   - Remove `public/` and `resources/` directories and rebuild

4. **Slow build times**
   - Use `hugo --gc` to clean up unused cache
   - Consider using `hugo server --disableFastRender` for development

### Getting Help

- [Hugo Documentation](https://gohugo.io/documentation/)
- [LoveIt Theme Documentation](https://hugoloveit.com/)
- [Hugo Community Forum](https://discourse.gohugo.io/)

## 🤝 Contributing

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Commit your changes**: `git commit -m 'Add some amazing feature'`
4. **Push to the branch**: `git push origin feature/amazing-feature`
5. **Open a Pull Request**

### Development Guidelines

- Follow Hugo best practices
- Test your changes locally before submitting
- Update documentation if needed
- Keep commits focused and descriptive

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Hugo](https://gohugo.io/) - The world's fastest framework for building websites
- [LoveIt Theme](https://github.com/dillonzq/LoveIt) - A clean, elegant but advanced Hugo theme
- [GitHub Pages](https://pages.github.com/) - Free hosting for static sites

## 📞 Contact

- **Author**: wenhaofree
- **Email**: <EMAIL>
- **GitHub**: [@wenhaofree](https://github.com/wenhaofree)

---

**Happy blogging! 🎉**