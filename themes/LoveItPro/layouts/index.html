{{- define "main" -}}
<div class="homepage">
    {{- /* Hero Carousel Section - Logbook Style */ -}}
    {{- if .Site.Params.header.carousel.enable -}}
        <div class="container">
            {{- partial "components/carousel.html" . -}}
        </div>
    {{- else -}}
        {{- /* Fallback Hero Section */ -}}
        <div class="container">
            <div class="hero-carousel">
                <div class="carousel-item">
                    {{- if .Site.Params.hero.background -}}
                        <div class="carousel-bg" style="background-image: url('{{ .Site.Params.hero.background }}');"></div>
                    {{- else -}}
                        <div class="carousel-bg" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);"></div>
                    {{- end -}}
                    <div class="carousel-overlay"></div>
                    <div class="carousel-content">
                        <div class="container">
                            <div class="row">
                                <div class="col-lg-8">
                                    <h1 class="carousel-title">{{ .Site.Title }}</h1>
                                    <p class="carousel-excerpt">{{ .Site.Params.description | default "欢迎来到我的博客" }}</p>
                                    <a href="{{ "posts" | relLangURL }}" class="btn btn-primary btn-lg">
                                        {{ i18n "viewAllPosts" | default "查看所有文章" }}
                                        <i class="fas fa-arrow-right ms-2"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {{- end -}}

    <div class="container">
        <div class="content-wrapper">
            {{- /* Main Content Area */ -}}
            <div class="main-column">
                {{- /* Featured Posts Section */ -}}
                {{- if .Site.Params.home.featured.enable -}}
                    <section class="featured-posts">
                        <h2 class="section-title">
                            {{ .Site.Params.home.featured.title | default (i18n "featuredPosts" | default "Featured Posts") }}
                        </h2>

                        <div class="blog-grid">
                            {{- $featured := where .Site.RegularPages "Params.featured" true -}}
                            {{- if not $featured -}}
                                {{- $featured = first (.Site.Params.home.featured.count | default 3) .Site.RegularPages -}}
                            {{- end -}}

                            {{- range $featured -}}
                                {{- partial "components/article-card.html" (dict "page" . "featured" true) -}}
                            {{- end -}}
                        </div>
                    </section>
                {{- end -}}

                {{- /* Recent Posts Section */ -}}
                {{- if .Site.Params.home.posts.enable -}}
                    <section class="recent-posts">
                        <h2 class="section-title">
                            {{ .Site.Params.home.posts.title | default (i18n "recentPosts" | default "Recent Posts") }}
                        </h2>

                        {{- /* Posts Grid */ -}}
                        <div class="blog-grid">
                            {{- $paginator := .Paginate (first (.Site.Params.home.posts.count | default 6) .Site.RegularPages) (.Site.Params.home.posts.paginate | default 6) -}}
                            {{- range $paginator.Pages -}}
                                {{- partial "components/article-card.html" (dict "page" .) -}}
                            {{- end -}}
                        </div>

                        {{- /* Pagination */ -}}
                        {{- if gt $paginator.TotalPages 1 -}}
                            <nav aria-label="{{ i18n "pagination" | default "Pagination" }}" class="mt-5">
                                {{- partial "components/pagination.html" $paginator -}}
                            </nav>
                        {{- end -}}

                        {{- /* Load More Button */ -}}
                        <div class="text-center mt-4">
                            <a href="{{ "posts" | relLangURL }}" class="btn btn-primary btn-lg">
                                {{ i18n "viewAllPosts" | default "View All Posts" }}
                                <i class="fas fa-arrow-right ms-2"></i>
                            </a>
                        </div>
                    </section>
                {{- end -}}
            </div>

            {{- /* Sidebar */ -}}
            {{- if .Site.Params.sidebar.enable -}}
                <div class="sidebar-column">
                    {{- partial "sidebar/sidebar.html" . -}}
                </div>
            {{- end -}}
        </div>
    </div>
</div>

{{- /* JSON-LD Structured Data */ -}}
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "Blog",
    "name": "{{ .Site.Title }}",
    "description": "{{ .Site.Params.description }}",
    "url": "{{ .Site.BaseURL }}",
    "author": {
        "@type": "Person",
        "name": "{{ .Site.Params.author }}"
    },
    "publisher": {
        "@type": "Organization",
        "name": "{{ .Site.Title }}",
        "logo": {
            "@type": "ImageObject",
            "url": "{{ .Site.Params.seo.image | absURL }}"
        }
    }
}
</script>
{{- end -}}
