{{- /* Article Card Component */ -}}
{{- $page := .page -}}
{{- $featured := .featured | default false -}}
{{- $compact := .compact | default false -}}

<article class="article-card{{ if $featured }} featured{{ end }}{{ if $compact }} compact{{ end }}">
    <div class="card h-100 shadow-sm">
        {{- /* Card Image */ -}}
        {{- if .Site.Params.article.card.showImage -}}
            <div class="card-img-container">
                <a href="{{ $page.RelPermalink }}" class="card-img-link">
                    {{- $image := $page.Params.image | default "/images/default-post.svg" -}}
                    <img src="{{ $image | relURL }}"
                         alt="{{ $page.Title }}"
                         class="card-img-top"
                         loading="lazy">
                </a>
                
                {{- /* Featured Badge */ -}}
                {{- if $featured -}}
                    <div class="featured-badge">
                        <i class="fas fa-star"></i>
                        {{ i18n "featured" | default "Featured" }}
                    </div>
                {{- end -}}
                
                {{- /* Category Badge */ -}}
                {{- with $page.Params.categories -}}
                    <div class="category-badge">
                        <a href="{{ printf "/categories/%s/" (index . 0 | urlize) | relLangURL }}">
                            {{ index . 0 }}
                        </a>
                    </div>
                {{- end -}}
            </div>
        {{- end -}}
        
        <div class="card-body d-flex flex-column">
            {{- /* Card Header */ -}}
            <div class="card-header-info">
                {{- /* Title */ -}}
                <h3 class="card-title{{ if $compact }} h6{{ else }} h5{{ end }}">
                    <a href="{{ $page.RelPermalink }}" class="text-decoration-none">
                        {{ $page.Title }}
                    </a>
                </h3>
                
                {{- /* Meta Information */ -}}
                <div class="card-meta">
                    {{- if .Site.Params.article.card.showDate -}}
                        <span class="meta-item">
                            <i class="fas fa-calendar-alt"></i>
                            <time datetime="{{ $page.Date.Format "2006-01-02T15:04:05Z07:00" }}">
                                {{ $page.Date.Format (.Site.Params.dateFormat | default "Jan 2, 2006") }}
                            </time>
                        </span>
                    {{- end -}}
                    
                    {{- if and .Site.Params.article.card.showAuthor ($page.Params.author | default .Site.Params.author) -}}
                        <span class="meta-item">
                            <i class="fas fa-user"></i>
                            {{ $page.Params.author | default .Site.Params.author }}
                        </span>
                    {{- end -}}
                    
                    {{- if and .Site.Params.article.card.showReadingTime .Site.Params.article.readingTime.enable -}}
                        <span class="meta-item">
                            <i class="fas fa-clock"></i>
                            {{ partial "components/reading-time.html" $page }}
                        </span>
                    {{- end -}}
                </div>
            </div>
            
            {{- /* Card Content */ -}}
            {{- if and .Site.Params.article.card.showExcerpt (not $compact) -}}
                <div class="card-text mt-2 flex-grow-1">
                    {{ $page.Summary | truncate 150 }}
                </div>
            {{- end -}}
            
            {{- /* Card Footer */ -}}
            <div class="card-footer-info mt-auto">
                {{- /* Tags */ -}}
                {{- if and .Site.Params.article.card.showTags $page.Params.tags (not $compact) -}}
                    <div class="card-tags mb-3">
                        {{- range first 3 $page.Params.tags -}}
                            <a href="{{ printf "/tags/%s/" (. | urlize) | relLangURL }}" class="tag-link">
                                #{{ . }}
                            </a>
                        {{- end -}}
                        {{- if gt (len $page.Params.tags) 3 -}}
                            <span class="tag-link">+{{ sub (len $page.Params.tags) 3 }}</span>
                        {{- end -}}
                    </div>
                {{- end -}}
                
                {{- /* Read More Button */ -}}
                <div class="card-actions">
                    <a href="{{ $page.RelPermalink }}" class="btn btn-primary btn-sm">
                        {{ i18n "readMore" | default "Read More" }}
                        <i class="fas fa-arrow-right ms-1"></i>
                    </a>

                    {{- /* Additional Actions */ -}}
                    <div class="card-actions-extra">
                        {{- /* Bookmark Button */ -}}
                        <button class="btn btn-outline-secondary btn-sm bookmark-btn"
                                data-url="{{ $page.RelPermalink }}"
                                data-title="{{ $page.Title }}"
                                title="{{ i18n "bookmark" | default "Bookmark" }}">
                            <i class="far fa-bookmark"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        {{- /* Card Overlay for Hover Effects */ -}}
        <div class="card-overlay"></div>
    </div>
</article>
