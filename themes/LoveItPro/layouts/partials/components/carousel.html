{{- /* Hero Carousel Component */ -}}
{{- $featured := where .Site.RegularPages "Params.featured" true -}}
{{- if not $featured -}}
    {{- $featured = first 5 .Site.RegularPages -}}
{{- end -}}

{{- if and $featured (gt (len $featured) 0) -}}
<section class="hero-carousel">
    <div id="heroCarousel" class="carousel slide" data-bs-ride="carousel"
         {{- if .Site.Params.header.carousel.autoplay }} data-bs-interval="{{ .Site.Params.header.carousel.interval | default 5000 }}"{{ else }} data-bs-interval="false"{{ end }}>
        
        {{- /* Carousel Indicators */ -}}
        {{- if .Site.Params.header.carousel.showIndicators -}}
            <div class="carousel-indicators">
                {{- range $index, $post := $featured -}}
                    <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="{{ $index }}" 
                            {{- if eq $index 0 }} class="active" aria-current="true"{{ end }} 
                            aria-label="{{ printf "Slide %d" (add $index 1) }}"></button>
                {{- end -}}
            </div>
        {{- end -}}
        
        {{- /* Carousel Items */ -}}
        <div class="carousel-inner">
            {{- range $index, $post := $featured -}}
                <div class="carousel-item{{ if eq $index 0 }} active{{ end }}">
                    {{- /* Background Image */ -}}
                    {{- $image := $post.Params.image | default "/images/default-post.svg" -}}
                    <div class="carousel-bg" style="background-image: url('{{ $image | relURL }}');"></div>
                    
                    {{- /* Overlay */ -}}
                    <div class="carousel-overlay"></div>
                    
                    {{- /* Content */ -}}
                    <div class="carousel-content">
                        <div class="container">
                            <div class="row">
                                <div class="col-lg-8">
                                    <div class="carousel-text">
                                        {{- /* Category Badge */ -}}
                                        {{- with $post.Params.categories -}}
                                            <span class="category-badge">{{ index . 0 }}</span>
                                        {{- end -}}
                                        
                                        {{- /* Title */ -}}
                                        <h2 class="carousel-title">
                                            <a href="{{ $post.RelPermalink }}">{{ $post.Title }}</a>
                                        </h2>
                                        
                                        {{- /* Excerpt */ -}}
                                        <p class="carousel-excerpt">
                                            {{ $post.Summary | truncate 150 }}
                                        </p>
                                        
                                        {{- /* Meta Information */ -}}
                                        <div class="carousel-meta">
                                            <span class="meta-item">
                                                <i class="fas fa-calendar-alt"></i>
                                                {{ $post.Date.Format (.Site.Params.dateFormat | default "January 2, 2006") }}
                                            </span>
                                            
                                            {{- if .Site.Params.article.readingTime.enable -}}
                                                <span class="meta-item">
                                                    <i class="fas fa-clock"></i>
                                                    {{ partial "components/reading-time.html" $post }}
                                                </span>
                                            {{- end -}}
                                            
                                            {{- with $post.Params.author | default .Site.Params.author -}}
                                                <span class="meta-item">
                                                    <i class="fas fa-user"></i>
                                                    {{ . }}
                                                </span>
                                            {{- end -}}
                                        </div>
                                        
                                        {{- /* Read More Button */ -}}
                                        <a href="{{ $post.RelPermalink }}" class="btn btn-primary btn-lg mt-3">
                                            {{ i18n "readMore" | default "Read More" }}
                                            <i class="fas fa-arrow-right ms-2"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            {{- end -}}
        </div>
        
        {{- /* Carousel Controls */ -}}
        {{- if .Site.Params.header.carousel.showControls -}}
            <button class="carousel-control-prev" type="button" data-bs-target="#heroCarousel" data-bs-slide="prev">
                <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                <span class="visually-hidden">{{ i18n "previous" | default "Previous" }}</span>
            </button>
            <button class="carousel-control-next" type="button" data-bs-target="#heroCarousel" data-bs-slide="next">
                <span class="carousel-control-next-icon" aria-hidden="true"></span>
                <span class="visually-hidden">{{ i18n "next" | default "Next" }}</span>
            </button>
        {{- end -}}
        
        {{- /* Search Bar Overlay */ -}}
        {{- if .Site.Params.header.search.enable -}}
            <div class="carousel-search">
                <div class="container">
                    <div class="row justify-content-center">
                        <div class="col-lg-6">
                            <div class="search-form">
                                <div class="input-group input-group-lg">
                                    <input type="text" class="form-control" id="hero-search" 
                                           placeholder="{{ .Site.Params.header.search.placeholder | default (i18n "searchPlaceholder" | default "Search articles...") }}"
                                           aria-label="{{ i18n "search" | default "Search" }}">
                                    <button class="btn btn-primary" type="button" id="hero-search-btn">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                                <div id="hero-search-results" class="search-results"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {{- end -}}
    </div>
</section>
{{- end -}}
