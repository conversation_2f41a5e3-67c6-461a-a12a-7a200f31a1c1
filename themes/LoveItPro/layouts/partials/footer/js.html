{{- /* JavaScript Assets with <PERSON> */ -}}

{{- /* Bootstrap JavaScript */ -}}
{{- if eq .Site.Params.assets.framework "bootstrap" -}}
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js" integrity="sha384-geWF76RCwLtnZ8qwWowPQNguL3RmwHVBC9FhGdlKrxdiJJigb/j/68SIy3Te4Bkz" crossorigin="anonymous"></script>
{{- end -}}

{{- /* Fuse.js for Search */ -}}
{{- if .Site.Params.header.search.enable -}}
    <script src="https://cdn.jsdelivr.net/npm/fuse.js@6.6.2/dist/fuse.min.js" integrity="sha512-Nqw1tH3mpavka9cQCc5zWWEZNfIPdOYyQFjlV1NvflEtQ0/XI6ZQ+H/D3YgJdqSUJlMLAPRj/oXlaHCFbFCjoQ==" crossorigin="anonymous"></script>
{{- end -}}

{{- /* Highlight.js for Syntax Highlighting */ -}}
{{- if .Site.Params.markup.highlight.enable -}}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/highlight.min.js" integrity="sha512-rdhY3cbXURo13l/WU9VlaRyaIYeJ/KBakckXIvJNAQde8DgpOmE+eZf7ha4vdqVjTtwQt69bD2wH2LXob/LB7Q==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script>hljs.highlightAll();</script>
{{- end -}}

{{- /* Main Theme JavaScript */ -}}
{{- $js := resources.Get "js/main.js" -}}
{{- if $js -}}
    {{- if hugo.IsProduction -}}
        {{- $js = $js | resources.Minify | resources.Fingerprint "sha256" -}}
        <script src="{{ $js.RelPermalink }}" integrity="{{ $js.Data.Integrity }}" crossorigin="anonymous"></script>
    {{- else -}}
        <script src="{{ $js.RelPermalink }}"></script>
    {{- end -}}
{{- end -}}

{{- /* Logbook Theme JavaScript */ -}}
{{- $logbookJS := resources.Get "js/logbook.js" -}}
{{- if $logbookJS -}}
    {{- if hugo.IsProduction -}}
        {{- $logbookJS = $logbookJS | resources.Minify | resources.Fingerprint "sha256" -}}
        <script src="{{ $logbookJS.RelPermalink }}" integrity="{{ $logbookJS.Data.Integrity }}" crossorigin="anonymous"></script>
    {{- else -}}
        <script src="{{ $logbookJS.RelPermalink }}"></script>
    {{- end -}}
{{- end -}}

{{- /* Search JavaScript */ -}}
{{- if .Site.Params.header.search.enable -}}
    {{- $searchJS := resources.Get "js/search.js" -}}
    {{- if $searchJS -}}
        {{- if hugo.IsProduction -}}
            {{- $searchJS = $searchJS | resources.Minify | resources.Fingerprint "sha256" -}}
            <script src="{{ $searchJS.RelPermalink }}" integrity="{{ $searchJS.Data.Integrity }}" crossorigin="anonymous"></script>
        {{- else -}}
            <script src="{{ $searchJS.RelPermalink }}"></script>
        {{- end -}}
    {{- end -}}
{{- end -}}

{{- /* Custom JavaScript Files */ -}}
{{- range .Site.Params.assets.customJS -}}
    {{- if hasPrefix . "http" -}}
        <script src="{{ . }}" crossorigin="anonymous"></script>
    {{- else -}}
        {{- $customJS := resources.Get . -}}
        {{- if $customJS -}}
            {{- if hugo.IsProduction -}}
                {{- $customJS = $customJS | resources.Minify | resources.Fingerprint "sha256" -}}
                <script src="{{ $customJS.RelPermalink }}" integrity="{{ $customJS.Data.Integrity }}" crossorigin="anonymous"></script>
            {{- else -}}
                <script src="{{ $customJS.RelPermalink }}"></script>
            {{- end -}}
        {{- end -}}
    {{- end -}}
{{- end -}}

{{- /* Analytics */ -}}
{{- if and .Site.Params.analytics.google.id hugo.IsProduction -}}
    <!-- Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id={{ .Site.Params.analytics.google.id }}"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', '{{ .Site.Params.analytics.google.id }}');
    </script>
{{- end -}}

{{- /* Theme Configuration */ -}}
<script>
window.themeConfig = {
    search: {
        enable: {{ .Site.Params.header.search.enable | default false }},
        maxResults: {{ .Site.Params.header.search.maxResults | default 10 }},
        placeholder: {{ .Site.Params.header.search.placeholder | default (i18n "searchPlaceholder" | default "Search articles...") | jsonify }}
    },
    carousel: {
        enable: {{ .Site.Params.header.carousel.enable | default false }},
        autoplay: {{ .Site.Params.header.carousel.autoplay | default true }},
        interval: {{ .Site.Params.header.carousel.interval | default 5000 }}
    },
    theme: {
        default: {{ .Site.Params.defaultTheme | default "auto" | jsonify }},
        storageKey: 'theme-preference'
    },
    i18n: {
        loading: {{ i18n "loading" | default "Loading..." | jsonify }},
        noResults: {{ i18n "noSearchResults" | default "No results found" | jsonify }},
        searchHint: {{ i18n "searchHint" | default "Search through all articles" | jsonify }},
        copied: {{ i18n "copied" | default "Copied!" | jsonify }},
        copyFailed: {{ i18n "copyFailed" | default "Copy failed" | jsonify }}
    }
};
</script>
