{{- /* Custom Head Content */ -}}
{{- /* This file can be used for custom head content */ -}}
{{- /* Users can add their own meta tags, scripts, or styles here */ -}}

{{- /* Google Fonts */ -}}
{{- if .Site.Params.fonts.googleFonts -}}
    {{- range .Site.Params.fonts.googleFonts -}}
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family={{ . | urlize }}&display=swap" rel="stylesheet">
    {{- end -}}
{{- end -}}

{{- /* Preload Critical Resources */ -}}
{{- if .Site.Params.performance.preload -}}
    {{- range .Site.Params.performance.preload -}}
        <link rel="preload" href="{{ .href }}" as="{{ .as }}"{{ with .type }} type="{{ . }}"{{ end }}{{ if .crossorigin }} crossorigin{{ end }}>
    {{- end -}}
{{- end -}}

{{- /* DNS Prefetch */ -}}
{{- if .Site.Params.performance.dnsPrefetch -}}
    {{- range .Site.Params.performance.dnsPrefetch -}}
        <link rel="dns-prefetch" href="{{ . }}">
    {{- end -}}
{{- end -}}

{{- /* Custom Meta Tags */ -}}
{{- if .Site.Params.customMeta -}}
    {{- range .Site.Params.customMeta -}}
        <meta name="{{ .name }}" content="{{ .content }}">
    {{- end -}}
{{- end -}}

{{- /* Custom Styles for Theme Fixes */ -}}
<style>
/* Fix for missing images */
.carousel-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Ensure proper spacing */
.homepage {
    padding-top: 0;
}

.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Card improvements */
.card {
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Navigation improvements */
.navbar {
    box-shadow: 0 2px 4px rgba(0,0,0,.1);
}

/* Footer improvements */
.site-footer {
    margin-top: 3rem;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .hero-section {
        padding: 3rem 0 !important;
    }

    .hero-section .display-4 {
        font-size: 2rem;
    }
}

/* Ensure Bootstrap classes work */
.btn {
    display: inline-block;
    padding: 0.375rem 0.75rem;
    margin-bottom: 0;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    text-align: center;
    text-decoration: none;
    vertical-align: middle;
    cursor: pointer;
    border: 1px solid transparent;
    border-radius: 0.375rem;
    transition: all 0.15s ease-in-out;
}

.btn-primary {
    color: #fff;
    background-color: var(--primary);
    border-color: var(--primary);
}

.btn-primary:hover {
    color: #fff;
    background-color: #0056b3;
    border-color: #004085;
}

.btn-lg {
    padding: 0.5rem 1rem;
    font-size: 1.25rem;
    border-radius: 0.5rem;
}
</style>
