// LoveItPro Theme Main Styles - Logbook Style
// ============================================

// Variables
// ---------
:root {
  // Colors - Logbook inspired palette
  --primary: #2563eb;
  --secondary: #64748b;
  --success: #10b981;
  --danger: #ef4444;
  --warning: #f59e0b;
  --info: #06b6d4;
  --light: #f8fafc;
  --dark: #1e293b;
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;

  // Typography
  --font-family-base: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
  --font-family-heading: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-size-base: 1rem;
  --line-height-base: 1.6;

  // Spacing
  --spacer: 1rem;
  --border-radius: 0.5rem;
  --border-radius-lg: 0.75rem;
  --box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --box-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --transition: all 0.2s ease-in-out;
  --border-color: var(--gray-200);
}

// Base Styles - Logbook Style
// ---------------------------
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
  line-height: var(--line-height-base);
  color: var(--gray-800);
  background-color: var(--gray-50);
  transition: var(--transition);
  margin: 0;
  padding: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// Container and Layout
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;

  @media (min-width: 640px) {
    padding: 0 1.5rem;
  }

  @media (min-width: 1024px) {
    padding: 0 2rem;
  }
}

.main-content {
  min-height: calc(100vh - 200px);
  padding: 2rem 0;
}

// Layout Styles - Logbook Style
// -----------------------------
.content-wrapper {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 3rem;
  margin-top: 2rem;

  @media (max-width: 991px) {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
}

.main-column {
  min-width: 0; // Prevent grid overflow
}

.sidebar-column {
  @media (max-width: 991px) {
    order: -1;
  }
}

// Blog Grid Layout
.blog-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

// Featured Posts Section
.featured-posts {
  margin-bottom: 3rem;

  .section-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 2rem;
    padding-bottom: 0.5rem;
    border-bottom: 3px solid var(--primary);
    display: inline-block;
  }
}

// Responsive Design
// -----------------
@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  .hero-carousel {
    height: 50vh;
    min-height: 300px;
    margin-bottom: 2rem;

    .carousel-title {
      font-size: 1.75rem;
    }

    .carousel-content {
      padding: 0 1rem;
    }
  }

  .blog-grid {
    gap: 1.5rem;
  }

  .article-card {
    margin-bottom: 1.5rem;

    .card-img-container .card-img-top {
      height: 200px;
    }
  }

  .sidebar {
    margin-top: 2rem;
  }
}

@media (max-width: 480px) {
  .hero-carousel {
    .carousel-title {
      font-size: 1.5rem;
    }

    .carousel-excerpt {
      font-size: 1rem;
    }
  }

  .article-card {
    .card-body {
      padding: 1rem;
    }

    .card-title {
      font-size: 1.125rem;
    }
  }
}

// Typography - Logbook Style
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-heading);
  font-weight: 700;
  line-height: 1.25;
  color: var(--gray-900);
  margin-bottom: 1rem;
}

h1 { font-size: 2.25rem; }
h2 { font-size: 1.875rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

p {
  margin-bottom: 1rem;
  color: var(--gray-600);
  line-height: 1.7;
}

a {
  color: var(--primary);
  text-decoration: none;
  transition: var(--transition);

  &:hover {
    color: var(--primary);
    opacity: 0.8;
  }
}

// Utility classes
.text-decoration-none {
  text-decoration: none !important;
}

.shadow-sm {
  box-shadow: var(--box-shadow) !important;
}

.shadow-lg {
  box-shadow: var(--box-shadow-lg) !important;
}

// Header Styles - Logbook Style
// -----------------------------
.site-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--gray-200);
  position: sticky;
  top: 0;
  z-index: 1000;
  transition: var(--transition);

  .navbar {
    padding: 1rem 0;
    transition: var(--transition);

    .navbar-brand {
      font-weight: 800;
      font-size: 1.75rem;
      font-family: var(--font-family-heading);
      color: var(--gray-900);
      text-decoration: none;
      letter-spacing: -0.025em;

      &:hover {
        color: var(--primary);
      }

      .logo-img {
        height: 40px;
        width: auto;
      }
    }

    .nav-link {
      color: var(--gray-600);
      font-weight: 500;
      padding: 0.5rem 1rem;
      transition: var(--transition);
      border-radius: var(--border-radius);

      &:hover {
        color: var(--primary);
        background-color: var(--gray-100);
      }

      &.active {
        color: var(--primary);
        background-color: var(--gray-100);
        font-weight: 600;
      }
    }
  }
}

// Hero Carousel - Logbook Style
// -----------------------------
.hero-carousel {
  position: relative;
  height: 60vh;
  min-height: 400px;
  max-height: 600px;
  overflow: hidden;
  border-radius: var(--border-radius-lg);
  margin-bottom: 3rem;
  box-shadow: var(--box-shadow-lg);

  .carousel-item {
    height: 100%;
    position: relative;
  }

  .carousel-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    transition: transform 0.3s ease;
  }

  .carousel-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(15, 23, 42, 0.8), rgba(30, 41, 59, 0.4));
  }

  .carousel-content {
    position: relative;
    z-index: 2;
    height: 100%;
    display: flex;
    align-items: center;
    color: white;
    padding: 0 2rem;
  }
  
  .carousel-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    
    a {
      color: inherit;
      text-decoration: none;
      
      &:hover {
        color: rgba(255, 255, 255, 0.8);
      }
    }
  }
  
  .carousel-excerpt {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    opacity: 0.9;
  }
  
  .carousel-meta {
    margin-bottom: 1.5rem;
    
    .meta-item {
      margin-right: 1.5rem;
      opacity: 0.8;
      
      i {
        margin-right: 0.5rem;
      }
    }
  }
  
  .category-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    background-color: var(--primary);
    color: white;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 1rem;
  }
  
  .carousel-search {
    position: absolute;
    bottom: 2rem;
    left: 0;
    right: 0;
    z-index: 3;
  }
}

// Article Cards - Logbook Style
// -----------------------------
.article-card {
  margin-bottom: 2rem;

  .card {
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    transition: var(--transition);
    overflow: hidden;
    background: white;
    box-shadow: var(--box-shadow);

    &:hover {
      transform: translateY(-2px);
      box-shadow: var(--box-shadow-lg);
      border-color: var(--gray-300);
    }
  }

  .card-img-container {
    position: relative;
    overflow: hidden;

    .card-img-top {
      height: 240px;
      object-fit: cover;
      transition: transform 0.3s ease;
      width: 100%;
    }

    &:hover .card-img-top {
      transform: scale(1.02);
    }

    .featured-badge {
      position: absolute;
      top: 1rem;
      left: 1rem;
      background: linear-gradient(135deg, var(--warning), #f59e0b);
      color: white;
      padding: 0.375rem 0.75rem;
      border-radius: var(--border-radius);
      font-size: 0.75rem;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.025em;
      box-shadow: var(--box-shadow);
    }

    .category-badge {
      position: absolute;
      top: 1rem;
      right: 1rem;

      a {
        background: linear-gradient(135deg, var(--primary), #1d4ed8);
        color: white;
        padding: 0.375rem 0.75rem;
        border-radius: var(--border-radius);
        font-size: 0.75rem;
        font-weight: 500;
        text-decoration: none;
        text-transform: uppercase;
        letter-spacing: 0.025em;
        box-shadow: var(--box-shadow);
        transition: var(--transition);

        &:hover {
          transform: translateY(-1px);
          box-shadow: var(--box-shadow-lg);
        }
      }
    }
  }

  .card-body {
    padding: 1.5rem;
  }

  .card-title {
    font-size: 1.25rem;
    font-weight: 700;
    line-height: 1.3;
    margin-bottom: 0.75rem;
    color: var(--gray-900);

    a {
      color: inherit;
      text-decoration: none;

      &:hover {
        color: var(--primary);
      }
    }
  }

  .card-text {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: 1rem;
    font-size: 0.95rem;
  }

  .card-meta {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;

    .meta-item {
      display: flex;
      align-items: center;
      margin-right: 1.5rem;
      font-size: 0.875rem;
      color: var(--gray-500);

      i {
        margin-right: 0.375rem;
        font-size: 0.75rem;
      }
    }
  }

  .card-tags {
    margin-bottom: 1rem;

    .tag-link {
      display: inline-block;
      margin-right: 0.5rem;
      margin-bottom: 0.25rem;
      padding: 0.25rem 0.5rem;
      background-color: var(--gray-100);
      color: var(--gray-600);
      border-radius: var(--border-radius);
      font-size: 0.75rem;
      text-decoration: none;
      transition: var(--transition);

      &:hover {
        background-color: var(--primary);
        color: white;
      }
    }
  }

  .card-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1rem;
    border-top: 1px solid var(--gray-200);
  }

  &.featured {
    .card {
      border: 2px solid var(--warning);
      box-shadow: 0 0 0 4px rgba(245, 158, 11, 0.1);
    }
  }

  &.compact {
    .card-title {
      font-size: 1.125rem;
    }

    .card-body {
      padding: 1.25rem;
    }
  }
}

// Sidebar Styles - Logbook Style
// ------------------------------
.sidebar {
  .sidebar-widget {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
    margin-bottom: 2rem;
    overflow: hidden;
    transition: var(--transition);

    &:hover {
      box-shadow: var(--box-shadow-lg);
      border-color: var(--gray-300);
    }

    .widget-header {
      background: linear-gradient(135deg, var(--gray-50), var(--gray-100));
      padding: 1.25rem;
      border-bottom: 1px solid var(--gray-200);

      .widget-title {
        margin: 0;
        font-size: 1.125rem;
        font-weight: 700;
        color: var(--gray-900);
        font-family: var(--font-family-heading);

        i {
          margin-right: 0.5rem;
          color: var(--primary);
          font-size: 1rem;
        }
      }
    }

    .widget-content {
      padding: 1.25rem;
    }
  }
  
  // About Widget
  .about-widget {
    .avatar-img {
      width: 80px;
      height: 80px;
      object-fit: cover;
    }
    
    .about-stats {
      .stat-item {
        .stat-number {
          font-size: 1.5rem;
          font-weight: 700;
          color: var(--primary);
        }
        
        .stat-label {
          font-size: 0.875rem;
          color: var(--secondary);
        }
      }
    }
    
    .about-social {
      .social-link {
        display: inline-block;
        width: 40px;
        height: 40px;
        line-height: 40px;
        text-align: center;
        border-radius: 50%;
        background-color: var(--light);
        color: var(--dark);
        margin: 0 0.25rem;
        transition: var(--transition);
        
        &:hover {
          background-color: var(--primary);
          color: white;
          transform: translateY(-2px);
        }
      }
    }
  }
  
  // Social Widget
  .social-widget {
    .social-link-item {
      margin-bottom: 0.5rem;
      
      .social-link {
        display: flex;
        align-items: center;
        padding: 0.75rem;
        border-radius: var(--border-radius);
        text-decoration: none;
        color: white;
        transition: var(--transition);
        
        &:hover {
          transform: translateX(5px);
          box-shadow: var(--box-shadow);
        }
        
        .social-icon {
          width: 40px;
          text-align: center;
          font-size: 1.2rem;
        }
        
        .social-info {
          flex: 1;
          margin-left: 0.75rem;
          
          .social-name {
            font-weight: 600;
          }
          
          .social-description {
            font-size: 0.875rem;
            opacity: 0.8;
          }
        }
        
        .social-arrow {
          opacity: 0.6;
        }
      }
    }
  }
}

// Footer Styles
// -------------
.site-footer {
  background-color: var(--dark);
  color: white;
  padding: 3rem 0 1rem;
  margin-top: 4rem;
  
  .footer-content {
    .footer-section {
      margin-bottom: 2rem;
      
      h5 {
        margin-bottom: 1rem;
        color: white;
      }
      
      ul {
        list-style: none;
        padding: 0;
        
        li {
          margin-bottom: 0.5rem;
          
          a {
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            transition: var(--transition);
            
            &:hover {
              color: white;
            }
          }
        }
      }
    }
  }
  
  .footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 1rem;
    margin-top: 2rem;
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
  }
}

// Utilities - Logbook Style
// -------------------------
.back-to-top {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, var(--primary), #1d4ed8);
  color: white;
  border: none;
  border-radius: var(--border-radius-lg);
  font-size: 1.2rem;
  cursor: pointer;
  transition: var(--transition);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  box-shadow: var(--box-shadow-lg);

  &.show {
    opacity: 1;
    visibility: visible;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }
}

// Button Styles - Logbook Style
// -----------------------------
.btn {
  font-weight: 500;
  border-radius: var(--border-radius);
  transition: var(--transition);

  &.btn-primary {
    background: linear-gradient(135deg, var(--primary), #1d4ed8);
    border: none;

    &:hover {
      transform: translateY(-1px);
      box-shadow: var(--box-shadow-lg);
    }
  }

  &.btn-outline-primary {
    border-color: var(--primary);
    color: var(--primary);

    &:hover {
      background: var(--primary);
      border-color: var(--primary);
      transform: translateY(-1px);
    }
  }
}

// Loading Animation
// -----------------
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid var(--gray-200);
  border-radius: 50%;
  border-top-color: var(--primary);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

// Smooth Animations
// -----------------
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

// Animation Classes
.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.fade-in {
  animation: fadeIn 0.4s ease-out;
}

// Stagger Animation for Cards
.blog-grid .article-card {
  animation: fadeInUp 0.6s ease-out;
  animation-fill-mode: both;
}

.blog-grid .article-card:nth-child(1) { animation-delay: 0.1s; }
.blog-grid .article-card:nth-child(2) { animation-delay: 0.2s; }
.blog-grid .article-card:nth-child(3) { animation-delay: 0.3s; }
.blog-grid .article-card:nth-child(4) { animation-delay: 0.4s; }
.blog-grid .article-card:nth-child(5) { animation-delay: 0.5s; }
.blog-grid .article-card:nth-child(6) { animation-delay: 0.6s; }

// Improved Focus States
// --------------------
*:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

.btn:focus,
.form-control:focus {
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

// Print Styles
// ------------
@media print {
  .header,
  .sidebar,
  .back-to-top,
  .card-actions {
    display: none !important;
  }

  .article-card .card {
    border: 1px solid #ddd !important;
    box-shadow: none !important;
  }
}

// Dark Mode
// ---------
[data-theme="dark"] {
  body {
    background-color: #1a1a1a;
    color: #e9ecef;
  }
  
  .sidebar-widget {
    background-color: #2d2d2d;
    color: #e9ecef;
    
    .widget-header {
      background-color: #404040;
      border-bottom-color: #555;
    }
  }
  
  .card {
    background-color: #2d2d2d;
    color: #e9ecef;
  }
  
  .navbar {
    background-color: #2d2d2d !important;
    
    .navbar-nav .nav-link {
      color: #e9ecef;
    }
  }
}

// Search Modal
// ------------
.search-modal {
  .search-results-list {
    max-height: 400px;
    overflow-y: auto;

    .search-result-item {
      padding: 1rem;
      border-bottom: 1px solid var(--border-color, #dee2e6);
      transition: var(--transition);

      &:hover {
        background-color: var(--light);
      }

      .result-title {
        font-weight: 600;
        margin-bottom: 0.5rem;

        a {
          color: inherit;
          text-decoration: none;

          &:hover {
            color: var(--primary);
          }
        }
      }

      .result-excerpt {
        color: var(--secondary);
        font-size: 0.875rem;
        margin-bottom: 0.5rem;
      }

      .result-meta {
        font-size: 0.75rem;
        color: var(--secondary);

        .meta-item {
          margin-right: 1rem;
        }
      }
    }
  }
}

// Responsive Design
// -----------------
@media (max-width: 768px) {
  .hero-carousel {
    height: 400px;

    .carousel-item {
      height: 400px;
    }

    .carousel-title {
      font-size: 1.8rem;
    }
  }

  .article-card .card:hover {
    transform: none;
  }

  .sidebar {
    margin-top: 2rem;
  }
}
